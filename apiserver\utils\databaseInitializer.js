const { Sequelize } = require('sequelize');
const path = require('path');
const { getEnvConfig } = require('./env');

const env = getEnvConfig();

const checkAndInitializeDatabase = async () => {
  try {
    const sequelize = new Sequelize(env.DB_NAME, env.DB_USER, env.DB_PASSWORD, {
      host: env.DB_HOST,
      dialect: env.DB_DIALECT || 'sqlite', // Add a default dialect if not specified
      logging: false,
    });

    await sequelize.authenticate();
    console.log('Database connection has been established successfully.');

    // Check if any tables exist
    const [results] = await sequelize.query("SELECT name FROM sqlite_master WHERE type='table' AND name!='sqlite_sequence';");
    if (results.length === 0) {
      console.log('No tables found in the database. Initializing database...');
      const initDatabase = require('../scripts/init-database');
      await initDatabase();
      console.log('Database initialization complete.');
    } else {
      console.log('Database already contains tables. No initialization needed.');
    }

    await sequelize.close();
  } catch (error) {
    console.error('Unable to connect to the database or check its status:', error);
    // If connection fails, assume database needs initialization or is not accessible
    console.log('Attempting to initialize database due to connection issue...');
    const initDatabase = require('../scripts/init-database');
    await initDatabase();
    console.log('Database initialization complete.');
  }
};

module.exports = { checkAndInitializeDatabase };